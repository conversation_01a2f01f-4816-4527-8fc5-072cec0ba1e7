"""
PRO Mode Environment Setup
Development environment configuration and dependency management for various programming languages.
"""

import os
import json
import subprocess
import asyncio
from typing import Dict, List, Any, Optional
from logger.log import logger
from BASE.actions.pro_mode.security import validate_path, get_safe_working_directory


async def process_environment_setup(
    setup_type: str,
    language: str = "auto",
    project_path: Optional[str] = None,
    package_manager: Optional[str] = None,
    requirements_file: Optional[str] = None,
    virtual_env_name: Optional[str] = None,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Configure development environments and dependencies.
    
    Args:
        setup_type: Type of setup ('detect', 'install_deps', 'create_venv', 'activate_venv', 'check_tools')
        language: Programming language
        project_path: Path to project directory
        package_manager: Specific package manager to use
        requirements_file: Path to requirements/dependencies file
        virtual_env_name: Name for virtual environment
        tool_id: Tool execution ID
        search_references: Search references object
    
    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Get safe project path
        work_dir = get_safe_working_directory(project_path)
        
        logger.info(f"[environment_setup] Setup type: {setup_type}, Language: {language}")
        
        if setup_type == "detect":
            result = await _detect_project_environment(work_dir)
            
        elif setup_type == "install_deps":
            result = await _install_dependencies(work_dir, language, package_manager, requirements_file)
            
        elif setup_type == "create_venv":
            result = await _create_virtual_environment(work_dir, language, virtual_env_name)
            
        elif setup_type == "activate_venv":
            result = await _activate_virtual_environment(work_dir, language, virtual_env_name)
            
        elif setup_type == "check_tools":
            result = await _check_development_tools(language)
            
        else:
            raise ValueError(f"Unsupported setup type: {setup_type}")
        
        result["setup_type"] = setup_type
        result["language"] = language
        result["project_path"] = work_dir
        
        logger.info(f"[environment_setup] {setup_type} completed")
        return result, search_references
        
    except Exception as e:
        logger.error(f"[environment_setup] Error: {str(e)}")
        return {"error": f"Environment setup failed: {str(e)}"}, search_references


async def _detect_project_environment(project_path: str) -> Dict[str, Any]:
    """Detect project type and environment from files."""
    detected_languages = []
    config_files = []
    
    # Check for common configuration files
    config_patterns = {
        "python": ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile", "environment.yml"],
        "node": ["package.json", "yarn.lock", "package-lock.json", "node_modules"],
        "java": ["pom.xml", "build.gradle", "gradle.properties"],
        "go": ["go.mod", "go.sum"],
        "rust": ["Cargo.toml", "Cargo.lock"],
        "php": ["composer.json", "composer.lock"],
        "ruby": ["Gemfile", "Gemfile.lock"],
        "dotnet": ["*.csproj", "*.sln", "project.json"],
        "docker": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"]
    }
    
    for language, patterns in config_patterns.items():
        for pattern in patterns:
            file_path = os.path.join(project_path, pattern)
            if os.path.exists(file_path):
                detected_languages.append(language)
                config_files.append({
                    "language": language,
                    "file": pattern,
                    "path": file_path
                })
                break
    
    # Check for source code files
    source_extensions = {
        ".py": "python",
        ".js": "node",
        ".ts": "node",
        ".jsx": "node",
        ".tsx": "node",
        ".java": "java",
        ".go": "go",
        ".rs": "rust",
        ".php": "php",
        ".rb": "ruby",
        ".cs": "dotnet",
        ".cpp": "cpp",
        ".c": "c"
    }
    
    source_files = {}
    for root, dirs, files in os.walk(project_path):
        # Skip hidden directories and common build directories
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'target', 'build']]
        
        for file in files:
            _, ext = os.path.splitext(file)
            if ext in source_extensions:
                lang = source_extensions[ext]
                if lang not in source_files:
                    source_files[lang] = 0
                source_files[lang] += 1
    
    # Determine primary language
    primary_language = "unknown"
    if detected_languages:
        primary_language = detected_languages[0]
    elif source_files:
        primary_language = max(source_files, key=source_files.get)
    
    return {
        "primary_language": primary_language,
        "detected_languages": list(set(detected_languages)),
        "config_files": config_files,
        "source_file_counts": source_files,
        "project_structure": _analyze_project_structure(project_path)
    }


async def _install_dependencies(project_path: str, language: str, package_manager: Optional[str], requirements_file: Optional[str]) -> Dict[str, Any]:
    """Install project dependencies."""
    
    # Auto-detect package manager if not specified
    if not package_manager:
        if language == "python":
            if os.path.exists(os.path.join(project_path, "Pipfile")):
                package_manager = "pipenv"
            elif os.path.exists(os.path.join(project_path, "pyproject.toml")):
                package_manager = "poetry"
            else:
                package_manager = "pip"
        elif language == "node":
            if os.path.exists(os.path.join(project_path, "yarn.lock")):
                package_manager = "yarn"
            else:
                package_manager = "npm"
        elif language == "java":
            if os.path.exists(os.path.join(project_path, "pom.xml")):
                package_manager = "maven"
            else:
                package_manager = "gradle"
        elif language == "go":
            package_manager = "go"
        elif language == "rust":
            package_manager = "cargo"
        elif language == "php":
            package_manager = "composer"
        elif language == "ruby":
            package_manager = "bundle"
    
    # Build install command
    commands = {
        "pip": ["pip", "install", "-r", requirements_file or "requirements.txt"],
        "pipenv": ["pipenv", "install"],
        "poetry": ["poetry", "install"],
        "npm": ["npm", "install"],
        "yarn": ["yarn", "install"],
        "maven": ["mvn", "install"],
        "gradle": ["gradle", "build"],
        "go": ["go", "mod", "download"],
        "cargo": ["cargo", "build"],
        "composer": ["composer", "install"],
        "bundle": ["bundle", "install"]
    }
    
    if package_manager not in commands:
        raise ValueError(f"Unsupported package manager: {package_manager}")
    
    command = commands[package_manager]
    
    # Execute installation
    try:
        process = await asyncio.create_subprocess_exec(
            *command,
            cwd=project_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        return {
            "package_manager": package_manager,
            "command": " ".join(command),
            "return_code": process.returncode,
            "success": process.returncode == 0,
            "stdout": stdout.decode('utf-8', errors='ignore'),
            "stderr": stderr.decode('utf-8', errors='ignore')
        }
        
    except FileNotFoundError:
        return {
            "package_manager": package_manager,
            "error": f"Package manager not found: {package_manager}",
            "success": False
        }


async def _create_virtual_environment(project_path: str, language: str, venv_name: Optional[str]) -> Dict[str, Any]:
    """Create virtual environment for the project."""
    
    if language == "python":
        venv_name = venv_name or "venv"
        venv_path = os.path.join(project_path, venv_name)
        
        # Create virtual environment
        command = ["python", "-m", "venv", venv_path]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "language": language,
                "venv_name": venv_name,
                "venv_path": venv_path,
                "command": " ".join(command),
                "success": process.returncode == 0,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except FileNotFoundError:
            return {
                "language": language,
                "error": "Python not found in PATH",
                "success": False
            }
    
    elif language == "node":
        # Node.js doesn't typically use virtual environments
        return {
            "language": language,
            "message": "Node.js uses local node_modules instead of virtual environments",
            "success": True
        }
    
    else:
        return {
            "language": language,
            "error": f"Virtual environments not supported for {language}",
            "success": False
        }


async def _activate_virtual_environment(project_path: str, language: str, venv_name: Optional[str]) -> Dict[str, Any]:
    """Provide activation instructions for virtual environment."""
    
    if language == "python":
        venv_name = venv_name or "venv"
        venv_path = os.path.join(project_path, venv_name)
        
        if not os.path.exists(venv_path):
            return {
                "language": language,
                "error": f"Virtual environment not found: {venv_path}",
                "success": False
            }
        
        # Provide activation commands for different platforms
        if os.name == 'nt':  # Windows
            activate_cmd = os.path.join(venv_path, "Scripts", "activate.bat")
            activate_ps = os.path.join(venv_path, "Scripts", "Activate.ps1")
        else:  # Unix/Linux/macOS
            activate_cmd = f"source {os.path.join(venv_path, 'bin', 'activate')}"
            activate_ps = None
        
        return {
            "language": language,
            "venv_name": venv_name,
            "venv_path": venv_path,
            "activation_commands": {
                "cmd": activate_cmd,
                "powershell": activate_ps,
                "bash": activate_cmd
            },
            "success": True
        }
    
    else:
        return {
            "language": language,
            "error": f"Virtual environment activation not applicable for {language}",
            "success": False
        }


async def _check_development_tools(language: str) -> Dict[str, Any]:
    """Check if development tools are installed and available."""
    
    tools_to_check = {
        "python": ["python", "pip", "python3", "pip3"],
        "node": ["node", "npm", "yarn"],
        "java": ["java", "javac", "mvn", "gradle"],
        "go": ["go"],
        "rust": ["rustc", "cargo"],
        "php": ["php", "composer"],
        "ruby": ["ruby", "gem", "bundle"],
        "dotnet": ["dotnet"],
        "auto": ["git", "docker", "kubectl"]
    }
    
    tools = tools_to_check.get(language, [])
    if language == "auto":
        # Check common development tools
        for lang_tools in tools_to_check.values():
            tools.extend(lang_tools)
        tools = list(set(tools))  # Remove duplicates
    
    tool_status = {}
    
    for tool in tools:
        try:
            process = await asyncio.create_subprocess_exec(
                tool, "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                version_output = stdout.decode('utf-8', errors='ignore').strip()
                tool_status[tool] = {
                    "available": True,
                    "version": version_output.split('\n')[0]  # First line usually contains version
                }
            else:
                tool_status[tool] = {
                    "available": False,
                    "error": stderr.decode('utf-8', errors='ignore').strip()
                }
                
        except FileNotFoundError:
            tool_status[tool] = {
                "available": False,
                "error": "Command not found"
            }
        except Exception as e:
            tool_status[tool] = {
                "available": False,
                "error": str(e)
            }
    
    available_tools = [tool for tool, status in tool_status.items() if status["available"]]
    missing_tools = [tool for tool, status in tool_status.items() if not status["available"]]
    
    return {
        "language": language,
        "tool_status": tool_status,
        "available_tools": available_tools,
        "missing_tools": missing_tools,
        "total_checked": len(tools),
        "total_available": len(available_tools)
    }


def _analyze_project_structure(project_path: str) -> Dict[str, Any]:
    """Analyze basic project structure."""
    structure = {
        "directories": [],
        "files": [],
        "total_files": 0,
        "total_directories": 0
    }
    
    try:
        for root, dirs, files in os.walk(project_path):
            # Skip hidden and build directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'target', 'build']]
            
            level = root.replace(project_path, '').count(os.sep)
            if level < 3:  # Only analyze up to 3 levels deep
                rel_path = os.path.relpath(root, project_path)
                if rel_path != '.':
                    structure["directories"].append(rel_path)
                
                for file in files[:10]:  # Limit to first 10 files per directory
                    if not file.startswith('.'):
                        structure["files"].append(os.path.join(rel_path, file) if rel_path != '.' else file)
            
            structure["total_files"] += len(files)
            structure["total_directories"] += len(dirs)
    
    except Exception as e:
        logger.warning(f"Could not analyze project structure: {str(e)}")
    
    return structure
