"""
PRO Mode File Operations
Comprehensive file system operations for CodeMate PRO mode including discovery, reading, creation, editing, and management.
"""

import os
import shutil
import glob
import fnmatch
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from logger.log import logger
from BASE.actions.pro_mode.security import validate_path, is_safe_operation, validate_file_extension, check_disk_space


async def process_file_discovery(
    search_type: str,
    query: str,
    path: str = ".",
    recursive: bool = True,
    include_hidden: bool = False,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Find files by name, pattern, or content across the project.
    
    Args:
        search_type: Type of search ('name', 'pattern', 'content', 'extension')
        query: Search query
        path: Root path to search from
        recursive: Whether to search recursively
        include_hidden: Whether to include hidden files
        tool_id: Tool execution ID
        search_references: Search references object
    
    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Validate and sanitize path
        safe_path = validate_path(path)
        if not safe_path:
            raise ValueError(f"Invalid or unsafe path: {path}")
        
        logger.info(f"[file_discovery] Starting {search_type} search for '{query}' in {safe_path}")
        
        results = []
        
        if search_type == "name":
            results = await _search_by_name(safe_path, query, recursive, include_hidden)
        elif search_type == "pattern":
            results = await _search_by_pattern(safe_path, query, recursive, include_hidden)
        elif search_type == "content":
            results = await _search_by_content(safe_path, query, recursive, include_hidden)
        elif search_type == "extension":
            results = await _search_by_extension(safe_path, query, recursive, include_hidden)
        else:
            raise ValueError(f"Unsupported search type: {search_type}")
        
        # Add results to search references
        if search_references:
            for result in results:
                search_references.add_search_result(
                    path=result["path"],
                    name=result["name"],
                    content="",
                    type="file"
                )
        
        response = {
            "search_type": search_type,
            "query": query,
            "total_results": len(results),
            "results": results[:50],  # Limit to first 50 results
            "truncated": len(results) > 50
        }
        
        logger.info(f"[file_discovery] Found {len(results)} results")
        success = "error"
        if results:
            success = "success"

        final_response = {"status": success, "content": response}

        return final_response, search_references
        
    except Exception as e:
        logger.error(f"[file_discovery] Error: {str(e)}")
        return {"error": f"File discovery failed: {str(e)}"}, search_references


async def _search_by_name(base_path: str, query: str, recursive: bool, include_hidden: bool) -> List[Dict[str, Any]]:
    """Search files by name pattern."""
    results = []
    pattern = f"*{query}*"
    
    if recursive:
        search_pattern = os.path.join(base_path, "**", pattern)
        matches = glob.glob(search_pattern, recursive=True)
    else:
        search_pattern = os.path.join(base_path, pattern)
        matches = glob.glob(search_pattern)
    
    for match in matches:
        if not include_hidden and any(part.startswith('.') for part in Path(match).parts):
            continue
            
        if os.path.isfile(match):
            results.append({
                "path": match,
                "name": os.path.basename(match),
                "size": os.path.getsize(match),
                "modified": os.path.getmtime(match),
                "type": "file"
            })
        elif os.path.isdir(match):
            results.append({
                "path": match,
                "name": os.path.basename(match),
                "type": "directory"
            })
    
    return results


async def _search_by_pattern(base_path: str, pattern: str, recursive: bool, include_hidden: bool) -> List[Dict[str, Any]]:
    """Search files by glob pattern."""
    results = []
    
    if recursive:
        search_pattern = os.path.join(base_path, "**", pattern)
        matches = glob.glob(search_pattern, recursive=True)
    else:
        search_pattern = os.path.join(base_path, pattern)
        matches = glob.glob(search_pattern)
    
    for match in matches:
        if not include_hidden and any(part.startswith('.') for part in Path(match).parts):
            continue
            
        if os.path.isfile(match):
            results.append({
                "path": match,
                "name": os.path.basename(match),
                "size": os.path.getsize(match),
                "modified": os.path.getmtime(match),
                "type": "file"
            })
    
    return results


async def _search_by_extension(base_path: str, extension: str, recursive: bool, include_hidden: bool) -> List[Dict[str, Any]]:
    """Search files by extension."""
    if not extension.startswith('.'):
        extension = '.' + extension
    
    pattern = f"*{extension}"
    return await _search_by_pattern(base_path, pattern, recursive, include_hidden)


async def _search_by_content(base_path: str, query: str, recursive: bool, include_hidden: bool) -> List[Dict[str, Any]]:
    """Search files by content."""
    results = []
    text_extensions = {'.py', '.js', '.ts', '.html', '.css', '.json', '.md', '.txt', '.yml', '.yaml', '.xml'}
    
    for root, dirs, files in os.walk(base_path):
        if not recursive:
            dirs.clear()
            
        if not include_hidden:
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            files = [f for f in files if not f.startswith('.')]
        
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            if file_ext in text_extensions:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if query.lower() in content.lower():
                            # Find line numbers where query appears
                            lines = content.split('\n')
                            matches = []
                            for i, line in enumerate(lines, 1):
                                if query.lower() in line.lower():
                                    matches.append({
                                        "line": i,
                                        "content": line.strip()
                                    })
                            
                            results.append({
                                "path": file_path,
                                "name": file,
                                "size": os.path.getsize(file_path),
                                "modified": os.path.getmtime(file_path),
                                "type": "file",
                                "matches": matches[:10]  # Limit to first 10 matches per file
                            })
                except Exception as e:
                    logger.warning(f"Could not search content in {file_path}: {str(e)}")
                    continue
    
    return results


async def process_file_read(
    file_paths: List[str],
    folder_path: str,
    encoding: str = "utf-8",
    line_range: Optional[Dict[str, int]] = None,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Read single or multiple files and analyze their content.
    
    Args:
        file_paths: List of file paths to read
        encoding: File encoding
        line_range: Optional line range to read
        tool_id: Tool execution ID
        search_references: Search references object
    
    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        results = []
        
        for file_path in file_paths:
            # Validate path
            safe_path = validate_path(file_path, folder_path=folder_path)
            if not safe_path:
                results.append({
                    "path": file_path,
                    "error": f"Invalid or unsafe path: {file_path}"
                })
                continue
            
            if not os.path.exists(safe_path):
                results.append({
                    "path": file_path,
                    "error": f"File not found: {file_path}"
                })
                continue
            
            if not os.path.isfile(safe_path):
                results.append({
                    "path": file_path,
                    "error": f"Path is not a file: {file_path}"
                })
                continue
            
            try:
                with open(safe_path, 'r', encoding=encoding, errors='ignore') as f:
                    if line_range:
                        lines = f.readlines()
                        start = max(0, line_range.get('start', 1) - 1)
                        end = min(len(lines), line_range.get('end', len(lines)))
                        content = ''.join(lines[start:end])
                        total_lines = len(lines)
                    else:
                        content = f.read()
                        total_lines = len(content.split('\n'))
                
                file_info = {
                    "path": file_path,
                    "name": os.path.basename(file_path),
                    "size": os.path.getsize(safe_path),
                    "modified": os.path.getmtime(safe_path),
                    "encoding": encoding,
                    "total_lines": total_lines,
                    "content": content
                }
                
                if line_range:
                    file_info["line_range"] = line_range
                
                results.append(file_info)
                
                # Add to search references
                if search_references:
                    search_references.add_search_result(
                        path=file_path,
                        name=os.path.basename(file_path),
                        content=content[:1000],  # First 1000 chars for reference
                        type="file"
                    )
                
            except Exception as e:
                results.append({
                    "path": file_path,
                    "error": f"Failed to read file: {str(e)}"
                })
        
        response = {
            "total_files": len(file_paths),
            "successful_reads": len([r for r in results if "error" not in r]),
            "failed_reads": len([r for r in results if "error" in r]),
            "results": results
        }
        
        logger.info(f"[file_read] Read {response['successful_reads']}/{response['total_files']} files successfully")
        success = "error"
        if response["successful_reads"] > 0:
            success = "success"
        final_response = {"status": success, "content": response}

        return final_response, search_references

    except Exception as e:
        logger.error(f"[file_read] Error: {str(e)}")
        result = {"status": "error", "content": f"File read failed: {str(e)}"}
        return result, search_references

@logger.catch()
async def process_file_create(
    file_path: str,
    folder_path: str,
    content: str,
    encoding: str = "utf-8",
    create_dirs: bool = False,
    overwrite: bool = False,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Create new files with specified content and structure.

    Args:
        file_path: Path where the new file should be created
        content: Content to write to the file
        encoding: File encoding
        create_dirs: Whether to create parent directories
        overwrite: Whether to overwrite if file exists
        tool_id: Tool execution ID
        search_references: Search references object

    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Validate and sanitize path
        logger.info(f"[file_create] Creating file: {file_path}")
        safe_path = validate_path(file_path,folder_path=folder_path, allow_creation=True)
        if not safe_path:
            raise ValueError(f"Invalid or unsafe path: {file_path}")

        # Check if file already exists
        if os.path.exists(safe_path) and not overwrite:
            raise ValueError(f"File already exists and overwrite is False: {file_path}")

        # Validate file extension
        if not validate_file_extension(safe_path):
            raise ValueError(f"File extension not allowed: {os.path.splitext(file_path)[1]}")

        # Create parent directories if needed
        parent_dir = os.path.dirname(safe_path)
        if not os.path.exists(parent_dir):
            if create_dirs:
                os.makedirs(parent_dir, exist_ok=True)
                logger.info(f"[file_create] Created directories: {parent_dir}")
            else:
                raise ValueError(f"Parent directory doesn't exist: {parent_dir}")

        # Check disk space (estimate content size + 10% buffer)
        content_size = len(content.encode(encoding)) * 1.1
        if not check_disk_space(parent_dir, int(content_size)):
            raise ValueError("Insufficient disk space")

        # Write file
        with open(safe_path, 'w', encoding=encoding) as f:
            f.write(content)

        # Get file info
        file_info = {
            "path": file_path,
            "name": os.path.basename(file_path),
            "size": os.path.getsize(safe_path),
            "created": True,
            "encoding": encoding,
            "lines": len(content.split('\n'))
        }

        # Add to search references
        if search_references:
            search_references.add_search_result(
                path=file_path,
                name=os.path.basename(file_path),
                content=content[:1000],
                type="file"
            )

        logger.info(f"[file_create] Successfully created file: {file_path}")
        succes = "error"
        if file_info:
            succes = "success"

        result = {"status": succes, "content": file_info}
        return result, search_references

    except Exception as e:
        logger.error(f"[file_create] Error: {str(e)}")
        result = {"status": "error", "content": f"File creation failed: {str(e)}"}

        return result, search_references


async def process_file_edit(
    file_path: str,
    edit_type: str,
    content: str = "",
    line_number: Optional[int] = None,
    line_range: Optional[Dict[str, int]] = None,
    search_pattern: Optional[str] = None,
    backup: bool = True,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Modify existing files with precise edits or complete rewrites.

    Args:
        file_path: Path to the file to edit
        edit_type: Type of edit ('replace', 'insert', 'append', 'prepend', 'delete_lines')
        content: New content for operations
        line_number: Line number for insert/delete operations
        line_range: Line range for replace/delete operations
        search_pattern: Pattern to search for when replacing
        backup: Whether to create backup before editing
        tool_id: Tool execution ID
        search_references: Search references object

    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Validate path
        safe_path = validate_path(file_path)
        if not safe_path:
            raise ValueError(f"Invalid or unsafe path: {file_path}")

        if not os.path.exists(safe_path):
            raise ValueError(f"File not found: {file_path}")

        if not os.path.isfile(safe_path):
            raise ValueError(f"Path is not a file: {file_path}")

        # Create backup if requested
        backup_path = None
        if backup:
            backup_path = safe_path + ".backup"
            shutil.copy2(safe_path, backup_path)
            logger.info(f"[file_edit] Created backup: {backup_path}")

        # Read current content
        with open(safe_path, 'r', encoding='utf-8', errors='ignore') as f:
            original_content = f.read()
            original_lines = original_content.split('\n')

        # Perform edit based on type
        if edit_type == "replace":
            if search_pattern:
                new_content = original_content.replace(search_pattern, content)
            elif line_range:
                start = max(0, line_range.get('start', 1) - 1)
                end = min(len(original_lines), line_range.get('end', len(original_lines)))
                new_lines = original_lines[:start] + content.split('\n') + original_lines[end:]
                new_content = '\n'.join(new_lines)
            else:
                new_content = content  # Complete replacement

        elif edit_type == "insert":
            if line_number is None:
                raise ValueError("Line number required for insert operation")
            insert_pos = max(0, min(line_number - 1, len(original_lines)))
            new_lines = original_lines[:insert_pos] + content.split('\n') + original_lines[insert_pos:]
            new_content = '\n'.join(new_lines)

        elif edit_type == "append":
            new_content = original_content + content

        elif edit_type == "prepend":
            new_content = content + original_content

        elif edit_type == "delete_lines":
            if line_range:
                start = max(0, line_range.get('start', 1) - 1)
                end = min(len(original_lines), line_range.get('end', len(original_lines)))
                new_lines = original_lines[:start] + original_lines[end:]
                new_content = '\n'.join(new_lines)
            elif line_number:
                line_pos = max(0, min(line_number - 1, len(original_lines)))
                new_lines = original_lines[:line_pos] + original_lines[line_pos + 1:]
                new_content = '\n'.join(new_lines)
            else:
                raise ValueError("Line number or range required for delete operation")
        else:
            raise ValueError(f"Unsupported edit type: {edit_type}")

        # Write modified content
        with open(safe_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        # Calculate changes
        original_line_count = len(original_lines)
        new_line_count = len(new_content.split('\n'))

        result = {
            "path": file_path,
            "edit_type": edit_type,
            "original_lines": original_line_count,
            "new_lines": new_line_count,
            "lines_changed": abs(new_line_count - original_line_count),
            "backup_created": backup_path is not None,
            "backup_path": backup_path,
            "success": True
        }

        # Add to search references
        if search_references:
            search_references.add_search_result(
                path=file_path,
                name=os.path.basename(file_path),
                content=new_content[:1000],
                type="file"
            )

        logger.info(f"[file_edit] Successfully edited file: {file_path} ({edit_type})")

        success = "error"
        if result:
            success = "success"

        final_response = {"status": success, "content": result}

        return final_response, search_references

    except Exception as e:
        logger.error(f"[file_edit] Error: {str(e)}")
        result = {"status": "error", "content": f"File edit failed: {str(e)}"}
        return result, search_references


async def process_file_manage(
    operation: str,
    source_path: str,
    destination_path: Optional[str] = None,
    recursive: bool = False,
    force: bool = False,
    create_parents: bool = False,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Copy, move, delete, rename files and directories.

    Args:
        operation: Operation to perform ('copy', 'move', 'delete', 'rename', 'mkdir', 'rmdir')
        source_path: Source file or directory path
        destination_path: Destination path (for copy, move, rename)
        recursive: Whether to perform operation recursively
        force: Whether to force operation (overwrite existing)
        create_parents: Whether to create parent directories
        tool_id: Tool execution ID
        search_references: Search references object

    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Validate operation safety
        is_safe, reason = is_safe_operation(operation, source_path, destination_path)
        if not is_safe:
            raise ValueError(f"Unsafe operation: {reason}")

        # Validate source path
        safe_source = validate_path(source_path)
        if not safe_source:
            raise ValueError(f"Invalid source path: {source_path}")

        result = {
            "operation": operation,
            "source_path": source_path,
            "destination_path": destination_path,
            "success": False
        }

        if operation == "delete":
            if not os.path.exists(safe_source):
                raise ValueError(f"Source path does not exist: {source_path}")

            if os.path.isfile(safe_source):
                os.remove(safe_source)
                result["deleted_type"] = "file"
            elif os.path.isdir(safe_source):
                if recursive:
                    shutil.rmtree(safe_source)
                    result["deleted_type"] = "directory_recursive"
                else:
                    os.rmdir(safe_source)
                    result["deleted_type"] = "directory"

            result["success"] = True
            logger.info(f"[file_manage] Deleted: {source_path}")

        elif operation == "mkdir":
            safe_dest = validate_path(source_path, allow_creation=True)
            if not safe_dest:
                raise ValueError(f"Invalid directory path: {source_path}")

            if create_parents:
                os.makedirs(safe_dest, exist_ok=True)
            else:
                os.mkdir(safe_dest)

            result["created_directory"] = source_path
            result["success"] = True
            logger.info(f"[file_manage] Created directory: {source_path}")

        elif operation == "rmdir":
            if not os.path.exists(safe_source):
                raise ValueError(f"Directory does not exist: {source_path}")

            if not os.path.isdir(safe_source):
                raise ValueError(f"Path is not a directory: {source_path}")

            if recursive:
                shutil.rmtree(safe_source)
            else:
                os.rmdir(safe_source)

            result["success"] = True
            logger.info(f"[file_manage] Removed directory: {source_path}")

        elif operation in ["copy", "move", "rename"]:
            if not destination_path:
                raise ValueError(f"Destination path required for {operation} operation")

            safe_dest = validate_path(destination_path, allow_creation=True)
            if not safe_dest:
                raise ValueError(f"Invalid destination path: {destination_path}")

            if not os.path.exists(safe_source):
                raise ValueError(f"Source path does not exist: {source_path}")

            # Check if destination exists and handle accordingly
            if os.path.exists(safe_dest) and not force:
                raise ValueError(f"Destination exists and force is False: {destination_path}")

            # Create parent directories if needed
            dest_parent = os.path.dirname(safe_dest)
            if not os.path.exists(dest_parent) and create_parents:
                os.makedirs(dest_parent, exist_ok=True)
                result["created_parents"] = True

            if operation == "copy":
                if os.path.isfile(safe_source):
                    shutil.copy2(safe_source, safe_dest)
                    result["copied_type"] = "file"
                elif os.path.isdir(safe_source):
                    if recursive:
                        shutil.copytree(safe_source, safe_dest, dirs_exist_ok=force)
                        result["copied_type"] = "directory_recursive"
                    else:
                        raise ValueError("Recursive flag required for directory copy")

            elif operation in ["move", "rename"]:
                shutil.move(safe_source, safe_dest)
                result["moved_type"] = "file" if os.path.isfile(safe_dest) else "directory"

            result["success"] = True
            logger.info(f"[file_manage] {operation.capitalize()}d: {source_path} -> {destination_path}")

            # Add destination to search references
            if search_references and os.path.isfile(safe_dest):
                search_references.add_search_result(
                    path=destination_path,
                    name=os.path.basename(destination_path),
                    content="",
                    type="file"
                )



        else:
            raise ValueError(f"Unsupported operation: {operation}")
        
        success = "error"
        if result["success"]:
            success = "success"

        final_response = {"status": success, "content": result}

        return final_response, search_references

    except Exception as e:
        logger.error(f"[file_manage] Error: {str(e)}")
        result = {"status": "error", "content": f"File management failed: {str(e)}"}
        return result, search_references
