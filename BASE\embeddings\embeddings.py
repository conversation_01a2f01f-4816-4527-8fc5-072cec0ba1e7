from typing import Union
from BASE.embeddings.local.embeddings import generate_local_embeddings
from BASE.embeddings.cloud.embeddings import generate_cloud_embeddings
from logger.log import logger

@logger.catch()
async def generate_embeddings_local(batch: bool, texts: Union[str, list[str]]) -> Union[list[float], list[list[float]]]:
    """Generate embeddings using local Ollama server."""
    return await generate_local_embeddings(batch, texts)

@logger.catch()
async def generate_embeddings_cloud(batch: bool, texts: Union[str, list[str]]) -> Union[list[float], list[list[float]]]:
    """Generate embeddings using cloud service."""
    return await generate_cloud_embeddings(batch, texts)


# Legacy compatibility - maintain the original class structure for existing code
class Embeddings:
    class generate:
        @staticmethod
        async def local(batch: bool, texts: Union[str, list[str]]):
            """Legacy wrapper for local embeddings."""
            return await generate_embeddings_local(batch, texts)

        @staticmethod
        async def cloud(batch: bool, texts: Union[str, list[str]]):
            """Legacy wrapper for cloud embeddings."""
            return await generate_embeddings_cloud(batch, texts)