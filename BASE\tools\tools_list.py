TOOLS_LIST = {
    "codebase": {
        "type": "function",
        "function": {
            "name": "context_search",
            "description": "Search for relevant information within a specific knowledge-base using semantic search.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information"
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge-base ID to search within"
                    }
                },
                "required": ["query", "kbid"],
            }
        }
    },
    "git": {
        "type": "function",
        "function": {
            "name": "context_search",
            "description": "Search for relevant information within a git repository knowledge-base using semantic search.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information"
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge-base ID to search within"
                    }
                },
                "required": ["query", "kbid"],
            }
        }
    },
    "docs": {
        "type": "function",
        "function": {
            "name": "context_search",
            "description": "Search for relevant information within a documentation knowledge-base using semantic search.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information"
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge-base ID to search within"
                    }
                },
                "required": ["query", "kbid"],
            }
        }
    },
    "folder": {
        "type": "function",
        "function": {
            "name": "folder_search",
            "description": "Search for relevant information within a specific folder using semantic search within a knowledge-base.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information"
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge-base ID to search within"
                    },
                    "folder_path": {
                        "type": "string",
                        "description": "The folder to search within"
                    }
                },
                "required": ["query", "kbid", "folder_path"],
            }
        }
    },
    "websearch": {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Search the web for relevant information and external resources.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information on the web"
                    }
                },
                "required": ["query"]
            }
        }
    },
    "swagger": {
        "type": "function",
        "function": {
            "name": "swagger_search",
            "description": "Search API documentation and endpoints using Swagger/OpenAPI specifications",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant API endpoints do not make it empty string or null or undefined i want to search for all endpoints "
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge base ID containing the Swagger documentation"
                    }
                },
                "required": ["query", "kbid"]
            }
        }
    },
    "agentic_search": {
        "type": "function",
        "function": {
            "name": "agentic_search",
            "description": "Search for relevant information within a specific knowledge-base using semantic search.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find relevant information"
                    },
                    "kbid": {
                        "type": "string",
                        "description": "The knowledge-base ID to search within"
                    }
                },
                "required": ["query", "kbid"],
            }
        }
    },
    # PRO MODE TOOLS
    "file_discovery": {
        "type": "function",
        "function": {
            "name": "file_discovery",
            "description": "Find files by name, pattern, or content across the project. Supports recursive search with filtering.",
            "parameters": {
                "type": "object",
                "properties": {
                    "search_type": {
                        "type": "string",
                        "enum": ["name", "pattern", "content", "extension"],
                        "description": "Type of search to perform"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query (filename, pattern, or content to search for)"
                    },
                    "path": {
                        "type": "string",
                        "description": "Root path to search from (defaults to project root)"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Whether to search recursively in subdirectories"
                    },
                    "include_hidden": {
                        "type": "boolean",
                        "description": "Whether to include hidden files and directories"
                    }
                },
                "required": ["search_type", "query"]
            }
        }
    },
    "file_read": {
        "type": "function",
        "function": {
            "name": "file_read",
            "description": "Read single or multiple files and analyze their content and structure.",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_paths": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of file paths to read"
                    },
                    "folder_path": {
                        "type": "string",
                        "description": "Path to the folder to read files from"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding (defaults to utf-8)"
                    },
                    "line_range": {
                        "type": "object",
                        "properties": {
                            "start": {"type": "integer"},
                            "end": {"type": "integer"}
                        },
                        "description": "Optional line range to read (for large files)"
                    }
                },
                "required": ["file_paths", "folder_path"]
            }
        }
    },
    "file_create": {
        "type": "function",
        "function": {
            "name": "file_create",
            "description": "Create new files with specified content and structure.",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path where the new file should be created make sure the file path is relative to the project root and not absolute path"
                    },
                    "folder_path": {
                        "type": "string",
                        "description": "Path to the folder where the new file should be created"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write to the file"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding (defaults to utf-8)"
                    },
                    "create_dirs": {
                        "type": "boolean",
                        "description": "Whether to create parent directories if they don't exist"
                    },
                    "overwrite": {
                        "type": "boolean",
                        "description": "Whether to overwrite if file already exists"
                    }
                },
                "required": ["file_path","folder_path", "content"]
            }
        }
    },
    "file_edit": {
        "type": "function",
        "function": {
            "name": "file_edit",
            "description": "Modify existing files with precise edits or complete rewrites.",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to edit"
                    },
                    "edit_type": {
                        "type": "string",
                        "enum": ["replace", "insert", "append", "prepend", "delete_lines"],
                        "description": "Type of edit operation to perform"
                    },
                    "content": {
                        "type": "string",
                        "description": "New content for replace/insert/append/prepend operations"
                    },
                    "line_number": {
                        "type": "integer",
                        "description": "Line number for insert/delete operations"
                    },
                    "line_range": {
                        "type": "object",
                        "properties": {
                            "start": {"type": "integer"},
                            "end": {"type": "integer"}
                        },
                        "description": "Line range for replace/delete operations"
                    },
                    "search_pattern": {
                        "type": "string",
                        "description": "Pattern to search for when replacing content"
                    },
                    "backup": {
                        "type": "boolean",
                        "description": "Whether to create a backup before editing"
                    }
                },
                "required": ["file_path", "edit_type"]
            }
        }
    },
    "file_manage": {
        "type": "function",
        "function": {
            "name": "file_manage",
            "description": "Copy, move, delete, rename files and directories.",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["copy", "move", "delete", "rename", "mkdir", "rmdir"],
                        "description": "File management operation to perform"
                    },
                    "source_path": {
                        "type": "string",
                        "description": "Source file or directory path"
                    },
                    "destination_path": {
                        "type": "string",
                        "description": "Destination path (for copy, move, rename operations)"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Whether to perform operation recursively (for directories)"
                    },
                    "force": {
                        "type": "boolean",
                        "description": "Whether to force operation (overwrite existing files)"
                    },
                    "create_parents": {
                        "type": "boolean",
                        "description": "Whether to create parent directories if they don't exist"
                    }
                },
                "required": ["operation", "source_path"]
            }
        }
    },
    "terminal_exec": {
        "type": "function",
        "function": {
            "name": "terminal_exec",
            "description": "Execute shell commands and capture output. Supports both synchronous and asynchronous execution.",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "Shell command to execute"
                    },
                    "working_directory": {
                        "type": "string",
                        "description": "Working directory for command execution (defaults to project root)"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Command timeout in seconds (defaults to 30)"
                    },
                    "capture_output": {
                        "type": "boolean",
                        "description": "Whether to capture and return command output"
                    },
                    "shell": {
                        "type": "boolean",
                        "description": "Whether to execute command through shell"
                    },
                    "env_vars": {
                        "type": "object",
                        "description": "Additional environment variables for command execution"
                    }
                },
                "required": ["command"]
            }
        }
    },
    "process_monitor": {
        "type": "function",
        "function": {
            "name": "process_monitor",
            "description": "Start, stop, and monitor running processes for development tools and servers.",
            "parameters": {
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": ["start", "stop", "status", "list", "kill"],
                        "description": "Process monitoring action to perform"
                    },
                    "process_name": {
                        "type": "string",
                        "description": "Name or identifier of the process"
                    },
                    "command": {
                        "type": "string",
                        "description": "Command to start the process (for start action)"
                    },
                    "working_directory": {
                        "type": "string",
                        "description": "Working directory for process execution"
                    },
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID for stop/kill operations"
                    },
                    "signal": {
                        "type": "string",
                        "description": "Signal to send when stopping process (SIGTERM, SIGKILL, etc.)"
                    }
                },
                "required": ["action"]
            }
        }
    },
    "environment_setup": {
        "type": "function",
        "function": {
            "name": "environment_setup",
            "description": "Configure development environments and dependencies for various programming languages.",
            "parameters": {
                "type": "object",
                "properties": {
                    "setup_type": {
                        "type": "string",
                        "enum": ["detect", "install_deps", "create_venv", "activate_venv", "check_tools"],
                        "description": "Type of environment setup operation"
                    },
                    "language": {
                        "type": "string",
                        "enum": ["python", "node", "java", "go", "rust", "php", "ruby", "auto"],
                        "description": "Programming language for environment setup"
                    },
                    "project_path": {
                        "type": "string",
                        "description": "Path to the project directory"
                    },
                    "package_manager": {
                        "type": "string",
                        "description": "Specific package manager to use (npm, yarn, pip, cargo, etc.)"
                    },
                    "requirements_file": {
                        "type": "string",
                        "description": "Path to requirements/dependencies file"
                    },
                    "virtual_env_name": {
                        "type": "string",
                        "description": "Name for virtual environment (Python)"
                    }
                },
                "required": ["setup_type"]
            }
        }
    }
}


def get(contexts: list, is_web_search: bool = False, mode: str = "NORMAL") -> list:
    """
    Returns a deduplicated list of tools based on provided contexts and web search flag.

    Args:
        contexts: List of context dictionaries containing type information
        is_web_search: Boolean flag to include web search tool
        mode: Mode string to include additional tools like agentic_search in PRO mode

    Returns:
        List of unique tool dictionaries (deduplicated by function name)
    """
    print(f"Getting tools list for contexts: {contexts}")

    # Early return for empty contexts unless web search is enabled or PRO mode
    if not contexts and not is_web_search and mode != "PRO":
        return []

    # Build initial tools list using list comprehension
    tools_list = [
        tool for context in (contexts or [])
        if (tool := TOOLS_LIST.get(context["type"])) is not None
    ]

    # Add websearch tool if requested
    if is_web_search:
        if (web_tool := TOOLS_LIST.get("websearch")):
            tools_list.append(web_tool)

    # Add PRO mode tools
    if mode == "PRO":
        pro_tools = [
            "agentic_search",
            # "file_discovery",
            "file_read",
            "file_create",
            "file_edit",
            "file_manage",
            "terminal_exec",
            "process_monitor",
            "environment_setup"
        ]

        for tool_name in pro_tools:
            if (pro_tool := TOOLS_LIST.get(tool_name)):
                tools_list.append(pro_tool)

    # Deduplicate tools by function name using dict for O(1) lookup
    unique_tools_dict = {}
    for tool in tools_list:
        function_name = tool["function"]["name"]
        if function_name not in unique_tools_dict:
            unique_tools_dict[function_name] = tool

    unique_tools = list(unique_tools_dict.values())

    print(f"Returning tools list: {unique_tools}")
    return unique_tools


# Example usage and test
# if __name__ == "__main__":
#     # Test case: git, codebase, and web search
#     test_contexts = [
#         {"type": "git"},
#         {"type": "codebase"},
#         {"type": "docs"}
#     ]
    
#     result = get(test_contexts, is_web_search=True)
#     print("\nTest Result:")
#     for tool in result:
#         print(f"- {tool['function']['name']}")
    
    # Expected output: context_search, web_search (only 2 tools, not 3)