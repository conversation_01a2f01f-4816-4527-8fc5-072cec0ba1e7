import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any


def xml_to_json(xml_str: str) -> Dict[str, Any]:
    """
    Convert XML string to JSON with specific structure for file monitor service.
    
    Expected output format:
    {
        "content": {"text": "content_text"},
        "additional_metadata": [
            {"file": "filename", "line_start": start, "line_end": end}
        ]
    }
    
    Args:
        xml_str: XML string to convert
        
    Returns:
        Dictionary with the converted structure
        
    Raises:
        ValueError: If XML is invalid or doesn't contain required elements
    """
    try:
        # Parse the XML string
        root = ET.fromstring(xml_str.strip())
        
        # Initialize result structure
        result = {
            "content": {"text": ""},
            "additional_metadata": []
        }
        
        # Extract content
        content_elem = root.find('content')
        if content_elem is not None and content_elem.text:
            result["content"]["text"] = content_elem.text.strip()
        
        # Extract references (optional)
        references_elem = root.find('references')
        if references_elem is not None:
            references = references_elem.findall('reference')
            
            for ref in references:
                try:
                    file_elem = ref.find('file')
                    lines_elem = ref.find('lines')
                    
                    if file_elem is not None and file_elem.text:
                        filename = file_elem.text.strip()
                        
                        # Extract line information
                        if lines_elem is not None:
                            line_elements = lines_elem.findall('line')
                            if line_elements:
                                # Get line numbers and find min/max
                                line_numbers = []
                                for line_elem in line_elements:
                                    if line_elem.text and line_elem.text.strip().isdigit():
                                        line_numbers.append(int(line_elem.text.strip()))
                                
                                if line_numbers:
                                    metadata_entry = {
                                        "file": filename,
                                        "line_start": min(line_numbers),
                                        "line_end": max(line_numbers)
                                    }
                                    result["additional_metadata"].append(metadata_entry)
                            else:
                                # No line elements, add file without line info
                                metadata_entry = {
                                    "file": filename,
                                    "line_start": None,
                                    "line_end": None
                                }
                                result["additional_metadata"].append(metadata_entry)
                        else:
                            # No lines element, add file without line info
                            metadata_entry = {
                                "file": filename,
                                "line_start": None,
                                "line_end": None
                            }
                            result["additional_metadata"].append(metadata_entry)
                            
                except Exception as e:
                    # Log the error but continue processing other references
                    print(f"Warning: Error processing reference: {e}")
                    continue
        
        return result
        
    except ET.ParseError as e:
        raise ValueError(f"Invalid XML format: {e}")
    except Exception as e:
        raise ValueError(f"Error processing XML: {e}")


def safe_xml_to_json(xml_str: str) -> Dict[str, Any]:
    """
    Safe wrapper for xml_to_json that returns a default structure on error.
    
    Args:
        xml_str: XML string to convert
        
    Returns:
        Dictionary with the converted structure or default structure on error
    """
    try:
        return xml_to_json(xml_str)
    except Exception as e:
        print(f"Error converting XML to JSON: {e}")
        return {
            "content": {"text": "Failed to parse XML"},
            "additional_metadata": []
        }


if __name__ == "__main__":
    # Test with the provided XML
    xml_str = """<search_response>
    <content>
        # Comprehensive Explanation of the File Monitor Service Codebase

        - The service is a Python-based standalone file monitor using `watchdog`.
        - It filters and debounces file events before notifying via HTTP.
        - HTTP notifications use RESTful POST with JSON to local endpoints.
        - An optional web server provides additional interface capabilities.
        - No direct database usage is evident in the file monitor service.
        - Extensive logging supports debugging and operational monitoring.
        - Configuration is flexible via CLI and JSON files.
    </content>
    <references>
        <reference>
            <file>standalone_file_monitor.py</file>
            <lines>
                <line>1</line>
                <line>100</line>
            </lines>
        </reference>
        <reference>
            <file>file_monitor.py</file>
            <lines>
                <line>1</line>
                <line>150</line>
            </lines>
        </reference>
        <reference>
            <file>file_monitor.log</file>
            <lines>
                <line>10</line>
                <line>100</line>
            </lines>
        </reference>
    </references>
</search_response>"""

    # Test the function
    print("=== Testing xml_to_json ===")
    result = xml_to_json(xml_str)
    print(json.dumps(result, indent=2))
    
    # Test with XML without references
    xml_no_refs = """<search_response>
    <content>
        This is content without references.
    </content>
</search_response>"""
    
    print("\n=== Testing without references ===")
    result_no_refs = xml_to_json(xml_no_refs)
    print(json.dumps(result_no_refs, indent=2))
    
    # Test with malformed XML using safe wrapper
    print("\n=== Testing with malformed XML ===")
    malformed_xml = "<search_response><content>Unclosed tag"
    result_safe = safe_xml_to_json(malformed_xml)
    print(json.dumps(result_safe, indent=2))
    
    # Test with empty XML
    print("\n=== Testing with minimal XML ===")
    minimal_xml = "<search_response></search_response>"
    result_minimal = xml_to_json(minimal_xml)
    print(json.dumps(result_minimal, indent=2))