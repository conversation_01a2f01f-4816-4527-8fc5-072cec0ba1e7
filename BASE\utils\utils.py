import os
import time
import psutil

from logger.log import logger

def log_memory_usage(stage: str):
    """
    LOG CURRENT MEMORY USAGE
    """
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        logger.debug(message=f"Memory usage at {stage}: {memory_mb:.1f} MB", extra={
            "_log_": "memory_usage"
        })
        return memory_mb
    except Exception as e:
        logger.warning(message=f"Failed to get memory usage at {stage}: {e}", extra={
            "_log_": "memory_usage"
        })
        return None