{"status": "success", "content": [{"endpoint": "/nge/prod/nge-api/api/persons/{personId}/chart/lab/panels/{panelId}", "content": {"responses": {"400": {"schema": {"$ref": "#/definitions/BadRequest1"}, "examples": {"application/json": {"message": "<string>"}}, "headers": {}, "description": "Bad Request"}, "404": {"headers": {}, "description": "Not Found"}, "403": {"schema": {"$ref": "#/definitions/Forbidden1"}, "examples": {"application/json": {"message": "<string>"}}, "headers": {}, "description": "Forbidden"}, "200": {"schema": {"default": "", "type": "object"}, "examples": {"application/json": {}}, "headers": {}, "description": "OK"}}, "operation_id": "Put{{baseUrl}}/persons/:personId/chart/lab/panels/:panelId", "summary": "{{baseUrl}}/persons/:personId/chart/lab/panels/:panelId", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "personId", "required": true, "description": "(Required) (Required) The id of the person whose observation panel is to be updated.", "type": "string"}, {"name": "panelId", "in": "path", "required": true, "description": "(Required) (Required) The id of the panel to be updated.", "type": "string"}, {"in": "header", "name": "Content-Type", "required": false, "description": "", "type": "string", "enum": ["application/json"]}, {"name": "Accept", "in": "header", "required": true, "description": "", "type": "string"}, {"schema": {"$ref": "#/definitions/%7B%7BbaseUrl%7D%7D~1persons~1%3ApersonId~1chart~1lab~1panels~1%3ApanelIdRequest"}, "in": "body", "name": "Body", "required": true, "description": ""}], "tags": ["Laboratory"], "description": "Update the specified observation panel for the specified personId.", "method": "PUT", "path": "/nge/prod/nge-api/api/persons/{personId}/chart/lab/panels/{panelId}"}, "additional_metadata": {"method": "PUT"}}]}