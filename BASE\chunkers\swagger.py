import os
import time
import json
import asyncio
from uuid import uuid4
from typing import Any, Callable, Coroutine
from BASE.services.swagger.swagger_llm_service import get_endpoint_summary_simple

from . import create_chunk, create_chunk_metadata





async def process_swagger_chunks(
    endpoints: list[dict],
    base_path: str,
    progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    
) -> list[dict]:

    """Process Swagger endpoints and return chunks as simple dictionaries."""
    print("Processing Swagger type knowledge base")
    endpoint_count = len(endpoints)
    print(f"Processing {endpoint_count} endpoints")

    # Log endpoint statistics
    methods = {}
    for endpoint in endpoints:
        method = endpoint.get("method", "unknown")
        methods[method] = methods.get(method, 0) + 1
    print(f"Endpoint methods distribution: {methods}")

    print(f"Processing {endpoint_count} endpoints with async processing")

    endpoint_processing_start = time.time()

    # Process endpoints in batches to avoid overwhelming the LLM service
    BATCH_SIZE = 10
    chunks = []
    processed_endpoints = 0

    for batch_start in range(0, endpoint_count, BATCH_SIZE):
        batch_end = min(batch_start + BATCH_SIZE, endpoint_count)
        batch_endpoints = endpoints[batch_start:batch_end]

        print(f"Processing batch {batch_start//BATCH_SIZE + 1}/{(endpoint_count + BATCH_SIZE - 1)//BATCH_SIZE}")

        # Generate summaries for this batch in parallel
        batch_summaries = await asyncio.gather(
            *[get_endpoint_summary_simple(endpoint) for endpoint in batch_endpoints],
            return_exceptions=True
        )

        # Process the batch results
        for i, (endpoint, summary) in enumerate(zip(batch_endpoints, batch_summaries)):
            global_index = batch_start + i

            # Handle exceptions from summary generation
            if isinstance(summary, Exception):
                print(f"Error generating summary for endpoint {global_index+1}: {summary}")
                summary = f"Error generating summary: {str(summary)}"
            print(f"Processing endpoint {global_index+1}/{endpoint_count}: {endpoint.get('path', 'unknown')}")

            endpoints_dir = os.path.join(base_path, "swagger_endpoints", str(uuid4()))
            os.makedirs(endpoints_dir, exist_ok=True)

            endpoint_file = os.path.join(endpoints_dir, f"{str(uuid4())}.json")
            endpoint_data = {
                "path": endpoint.get("path", ""),
                "summary": summary,
                "endpoint": endpoint,
            }

            with open(endpoint_file, "w+") as f:
                f.write(json.dumps(endpoint_data, indent=2))

            try:
                file_size = os.path.getsize(endpoint_file)
                print(f"Saved endpoint data to: {endpoint_file} (size: {file_size} bytes)")
            except OSError:
                print(f"Saved endpoint data to: {endpoint_file}")

            metadata = create_chunk_metadata(
                chunk_id=str(uuid4()),
                file_path=endpoint_file,
                name=endpoint.get("path", ""),
                content=summary,
                additional_metadata=endpoint
            )
            chunk = create_chunk(metadata=metadata, embeddings=[])
            chunks.append(chunk)

            processed_endpoints += 1
            progress_pct = (processed_endpoints / endpoint_count) * 100
            print(f"Progress: {progress_pct:.2f}%")
            if progress_callback:
                await progress_callback(progress_pct)

    endpoint_processing_time = time.time() - endpoint_processing_start
    print(f"Endpoint processing completed in {endpoint_processing_time:.2f} seconds (total chunks: {len(chunks)})")

    return chunks
