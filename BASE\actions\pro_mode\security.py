"""
Security and validation utilities for PRO mode operations.
Ensures all file system and terminal operations are safe and within project boundaries.
"""

import os
import re
from typing import Optional
from logger.log import logger


# Security configuration
ALLOWED_EXTENSIONS = {
    '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss', '.sass',
    '.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.csv', '.sql',
    '.java', '.go', '.rs', '.cpp', '.c', '.h', '.hpp', '.cs', '.php',
    '.rb', '.swift', '.kt', '.dart', '.scala', '.sh', '.bat', '.ps1',
    '.dockerfile', '.gitignore', '.env', '.ini', '.cfg', '.toml',
    '.lock', '.log', '.config', '.properties', '.gradle', '.maven'
}

FORBIDDEN_PATHS = {
    '/etc', '/usr', '/bin', '/sbin', '/boot', '/dev', '/proc', '/sys',
    'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
    '/System', '/Library', '/Applications'
}

FORBIDDEN_COMMANDS = {
    'rm -rf /', 'del /f /s /q', 'format', 'fdisk', 'mkfs',
    'sudo rm', 'sudo rmdir', 'sudo chmod 777', 'chmod 777',
    'chown root', 'passwd', 'useradd', 'userdel', 'groupadd',
    'systemctl', 'service', 'init', 'reboot', 'shutdown',
    'dd if=', 'wget', 'curl -o', 'nc -l', 'netcat'
}

SAFE_COMMANDS = {
    'ls', 'dir', 'pwd', 'cd', 'cat', 'type', 'head', 'tail',
    'grep', 'find', 'echo', 'printf', 'wc', 'sort', 'uniq',
    'git', 'npm', 'yarn', 'pip', 'python', 'node', 'java',
    'mvn', 'gradle', 'cargo', 'go', 'rustc', 'gcc', 'make',
    'docker', 'kubectl', 'helm', 'terraform'
}

@logger.catch()
def validate_path(path: str, folder_path: str = None, allow_creation: bool = False) -> Optional[str]:
    """
    Validate and sanitize file paths to prevent directory traversal and unauthorized access.
    
    Args:
        path: Path to validate
        folder_path: Parent folder path to check against
        allow_creation: Whether to allow paths that don't exist yet
        
    Returns:
        Sanitized path if valid, None if invalid
    """
    logger.info(f"Validating path: {path}, folder_path: {folder_path}, allow_creation: {allow_creation}")
    try:
        
        # If folder_path is provided, check if path is within that folder
        if not folder_path:
            logger.error("Folder path is required")
            return None
        
        
        # Check against forbidden system paths
        for forbidden in FORBIDDEN_PATHS:
            if folder_path.startswith(forbidden):
                logger.warning(f"Access to forbidden path: {folder_path}")
                return None
        
        # If path doesn't exist and creation is not allowed, check parent directory
        if not allow_creation and not os.path.exists(folder_path):
            parent_dir = os.path.dirname(folder_path)
            if not os.path.exists(parent_dir):
                logger.warning(f"Parent directory doesn't exist: {parent_dir}")
                return None
        
        return folder_path
        
    except Exception as e:
        logger.error(f"Path validation error: {str(e)}")
        return None


def validate_file_extension(file_path: str) -> bool:
    """
    Check if file extension is allowed for operations.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if extension is allowed, False otherwise
    """
    _, ext = os.path.splitext(file_path.lower())
    
    # Allow files without extensions (like Dockerfile, Makefile, etc.)
    if not ext:
        filename = os.path.basename(file_path).lower()
        common_no_ext = {'dockerfile', 'makefile', 'readme', 'license', 'changelog'}
        return any(filename.startswith(name) for name in common_no_ext)
    
    return ext in ALLOWED_EXTENSIONS


def validate_command(command: str) -> tuple[bool, str]:
    """
    Validate shell commands for safety.
    
    Args:
        command: Command to validate
        
    Returns:
        Tuple of (is_safe, reason)
    """
    command_lower = command.lower().strip()
    
    # Check for forbidden commands
    for forbidden in FORBIDDEN_COMMANDS:
        if forbidden in command_lower:
            return False, f"Forbidden command detected: {forbidden}"
    
    # Check for dangerous patterns
    dangerous_patterns = [
        r'rm\s+-rf\s+/',
        r'del\s+/[fs]\s+',
        r'>\s*/dev/',
        r'chmod\s+777',
        r'chown\s+root',
        r'sudo\s+rm',
        r'sudo\s+chmod',
        r'sudo\s+chown'
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, command_lower):
            return False, f"Dangerous command pattern detected: {pattern}"
    
    # Extract base command
    base_command = command_lower.split()[0] if command_lower.split() else ""
    
    # Check if base command is in safe list
    if base_command in SAFE_COMMANDS:
        return True, "Safe command"
    
    # Allow common development tools
    dev_tools = ['python3', 'node', 'npm', 'yarn', 'pip3', 'pipenv', 'poetry']
    if base_command in dev_tools:
        return True, "Development tool command"
    
    # Be cautious with unknown commands
    return False, f"Unknown or potentially unsafe command: {base_command}"


def is_safe_operation(operation: str, source_path: str, destination_path: str = None) -> tuple[bool, str]:
    """
    Check if a file operation is safe to perform.
    
    Args:
        operation: Type of operation (copy, move, delete, etc.)
        source_path: Source file/directory path
        destination_path: Destination path (if applicable)
        
    Returns:
        Tuple of (is_safe, reason)
    """
    # Validate source path
    safe_source = validate_path(source_path)
    if not safe_source:
        return False, f"Invalid source path: {source_path}"
    
    # Validate destination path if provided
    if destination_path:
        safe_dest = validate_path(destination_path, allow_creation=True)
        if not safe_dest:
            return False, f"Invalid destination path: {destination_path}"
    
    # Check operation-specific safety
    if operation == "delete":
        # Extra caution for delete operations
        if os.path.isdir(safe_source):
            # Don't allow deletion of important directories
            important_dirs = {'.git', 'node_modules', 'venv', '.venv', '__pycache__'}
            dir_name = os.path.basename(safe_source)
            if dir_name in important_dirs:
                return False, f"Cannot delete important directory: {dir_name}"
        
        # Don't allow deletion of important files
        important_files = {'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'pom.xml'}
        file_name = os.path.basename(safe_source)
        if file_name in important_files:
            return False, f"Cannot delete important file: {file_name}"
    
    elif operation in ["copy", "move"]:
        if destination_path:
            # Check if we're overwriting an important file
            if os.path.exists(safe_dest):
                important_files = {'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'pom.xml'}
                dest_name = os.path.basename(safe_dest)
                if dest_name in important_files:
                    return False, f"Cannot overwrite important file: {dest_name}"
    
    return True, "Operation is safe"


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename to remove dangerous characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = "unnamed_file"
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        sanitized = name[:255-len(ext)] + ext
    
    return sanitized


def get_safe_working_directory(requested_dir: str = None) -> str:
    """
    Get a safe working directory for operations.
    
    Args:
        requested_dir: Requested working directory
        
    Returns:
        Safe working directory path
    """
    if requested_dir:
        safe_dir = validate_path(requested_dir)
        if safe_dir and os.path.isdir(safe_dir):
            return safe_dir
    
    # Default to current working directory (project root)
    return os.getcwd()


def check_disk_space(path: str, required_bytes: int = 1024 * 1024) -> bool:
    """
    Check if there's enough disk space for an operation.
    
    Args:
        path: Path to check disk space for
        required_bytes: Required space in bytes
        
    Returns:
        True if enough space available, False otherwise
    """
    try:
        stat = os.statvfs(path) if hasattr(os, 'statvfs') else None
        if stat:
            available_bytes = stat.f_bavail * stat.f_frsize
            return available_bytes >= required_bytes
        else:
            # On Windows, use shutil.disk_usage
            import shutil
            _, _, free_bytes = shutil.disk_usage(path)
            return free_bytes >= required_bytes
    except Exception as e:
        logger.warning(f"Could not check disk space: {str(e)}")
        return True  # Assume space is available if we can't check
