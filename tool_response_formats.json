{"tool_response_formats": {"description": "Comprehensive documentation of response formats for tool call executions in CodeMate.AI", "version": "1.0", "last_updated": "2025-01-29", "general_structure": {"description": "All tool responses follow a consistent structure", "format": {"status": {"type": "string", "values": ["success", "error"], "description": "Indicates whether the tool execution was successful or encountered an error"}, "content": {"type": "varies", "description": "The actual response data - structure varies by tool and status"}}}, "tools": {"context_search": {"description": "Search for relevant information within a specific knowledge-base using semantic search", "parameters": {"query": "string - The search query to find relevant information", "kbid": "string - The knowledge-base ID to search within"}, "success_response": {"status": "success", "content": {"type": "array", "description": "Array of search result objects", "structure": [{"file": "string - Full file path", "content": {"text": "string - The actual content/text of the search result"}, "additional_metadata": {"line_start": "number|null - Starting line number of the content", "line_end": "number|null - Ending line number of the content"}}]}, "example": {"status": "success", "content": [{"file": "/path/to/file.py", "content": {"text": "def example_function():\n    return 'Hello World'"}, "additional_metadata": {"line_start": 10, "line_end": 12}}]}}, "error_response": {"status": "error", "content": {"type": "string", "description": "Descriptive error message explaining what went wrong"}, "examples": {"no_results": {"status": "error", "content": "No search results found for the specified knowledge base and query"}, "exception": {"status": "error", "content": "Context search failed: Session is required for cloud search authentication"}}}}, "folder_search": {"description": "Search for relevant information within a specific folder using semantic search within a knowledge-base", "parameters": {"query": "string - The search query to find relevant information", "kbid": "string - The knowledge-base ID to search within", "folder_path": "string - The folder to search within"}, "success_response": {"status": "success", "content": {"type": "array", "description": "Array of search result objects (consistent with context_search)", "structure": [{"file": "string - Full file path", "content": {"text": "string - The actual content/text of the search result"}, "additional_metadata": {"line_start": "number|null - Starting line number of the content", "line_end": "number|null - Ending line number of the content"}}]}, "example": {"status": "success", "content": [{"file": "/path/to/folder/file.js", "content": {"text": "const example = 'test';"}, "additional_metadata": {}}]}}, "error_response": {"status": "error", "content": {"type": "string", "description": "Descriptive error message explaining what went wrong"}, "examples": {"no_results": {"status": "error", "content": "No search results found for the specified folder and query"}, "exception": {"status": "error", "content": "Folder search failed: Knowledge base not found"}}}}, "web_search": {"description": "Search the web for relevant information and external resources", "parameters": {"query": "string - The search query to find relevant information on the web"}, "success_response": {"status": "success", "content": {"type": "string", "description": "Web search results content from external API", "note": "Content structure depends on external web search API response"}, "example": {"status": "success", "content": "Web search results containing relevant information about the query..."}}, "error_response": {"status": "error", "content": {"type": "string", "description": "Descriptive error message explaining what went wrong"}, "examples": {"no_results": {"status": "error", "content": "No web search results found for the specified query"}, "exception": {"status": "error", "content": "Web search failed: Session is required for web search authentication"}}}}, "swagger_search": {"description": "Search API documentation and endpoints using Swagger/OpenAPI specifications (currently commented out)", "status": "disabled", "parameters": {"query": "string - The search query to find relevant API endpoints", "kbid": "string - The knowledge base ID containing the Swagger documentation"}, "success_response": {"status": "success", "content": {"type": "string", "description": "JSON stringified array of API endpoint specifications"}}, "error_response": {"status": "error", "content": {"type": "string", "description": "JSON stringified empty array or error message"}}}}, "error_handling": {"description": "How errors are handled across all tools", "exception_response": {"tool_call_id": "string - The ID of the tool call that failed", "content": {"type": "string", "description": "JSON stringified error object", "structure": {"error": "string - Error message", "status": "error"}}}, "example": {"tool_call_id": "call_123456", "content": "{\"error\":\"Session is required for web search authentication\",\"status\":\"error\"}"}, "common_errors": [{"type": "authentication_error", "message": "Session is required for [tool] authentication", "cause": "Missing or invalid session for cloud-based operations"}, {"type": "unknown_tool_error", "message": "Unknown tool function: [function_name]", "cause": "Tool function not recognized by execute_tool_call"}, {"type": "search_error", "message": "Various search-related errors", "cause": "Issues with vector database, embeddings, or search operations"}]}, "frontend_integration": {"description": "How tool responses are delivered to the frontend", "streaming_format": {"type": "tool_call_complete", "tool_call": {"id": "string - Tool call ID", "name": "string - Tool function name", "result": "object - Parsed tool response (status + content)", "metadata": {"status": "string - 'completed' or 'error'", "timestamp": "number - Unix timestamp", "error": "string - Error message (only present if status is 'error')"}}}, "success_example": {"type": "tool_call_complete", "tool_call": {"id": "call_123456", "name": "context_search", "result": {"status": "success", "content": [{"file": "/path/to/file.py", "content": {"text": "code content"}, "additional_metadata": {"line_start": 1, "line_end": 10}}]}, "metadata": {"status": "completed", "timestamp": 1706553600}}}, "error_example": {"type": "tool_call_complete", "tool_call": {"id": "call_123456", "name": "web_search", "result": {"error": "Session is required for web search authentication", "status": "error"}, "metadata": {"status": "error", "timestamp": 1706553600, "error": "Session is required for web search authentication"}}}}, "notes": {"content_serialization": "Tool responses are JSON stringified when passed between components, then parsed back to objects for frontend delivery", "search_references": "All search tools populate a SearchReferences object that tracks search results for the request", "standardization_completed": "All tools now return consistent response formats as of 2025-01-29", "fixes_applied": ["Fixed critical syntax bug in web_search.py line 54 (missing quotes around 'status' key)", "Standardized success responses: all tools now return raw data structures (not JSON strings)", "Standardized error responses: all tools now return descriptive error messages as strings", "Improved exception handling: all tools now return proper error responses instead of raising exceptions"], "authentication": "Most tools require a valid session for cloud-based operations", "error_handling": "All tools now return consistent error format with descriptive messages instead of empty arrays or exceptions"}}}