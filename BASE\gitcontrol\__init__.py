"""
GitControl - A comprehensive Git management system for Python

Usage:
    from gitcontrol import GitControl
    
    gc = GitControl()
    repos = gc.list_git_repositories()
    gc.clone_repository("https://github.com/user/repo.git")
"""

from .gitcontrol import GitControl, GitControlError
from .config import GitControlConfig

__version__ = "1.0.0"
__all__ = ["GitControl", "GitControlConfig", "GitControlError"]

# Convenience function for quick usage
def create_git_controller(config_file=None):
    """Create a GitControl instance with optional custom config file"""
    return GitControl(config_file) if config_file else GitControl()
