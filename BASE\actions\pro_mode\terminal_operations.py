"""
PRO Mode Terminal Operations
Terminal execution, process monitoring, and environment setup tools for CodeMate PRO mode.
"""

import os
import subprocess
import psutil
import asyncio
import signal
import json
import time
from typing import Dict, List, Any, Optional, Union
from logger.log import logger
from BASE.actions.pro_mode.security import validate_command, get_safe_working_directory


# Global process registry for monitoring
PROCESS_REGISTRY = {}


async def process_terminal_exec(
    command: str,
    working_directory: Optional[str] = None,
    timeout: int = 30,
    capture_output: bool = True,
    shell: bool = True,
    env_vars: Optional[Dict[str, str]] = None,
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Execute shell commands and capture output.
    
    Args:
        command: Shell command to execute
        working_directory: Working directory for execution
        timeout: Command timeout in seconds
        capture_output: Whether to capture output
        shell: Whether to execute through shell
        env_vars: Additional environment variables
        tool_id: Tool execution ID
        search_references: Search references object
    
    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        # Validate command safety
        is_safe, reason = validate_command(command)
        if not is_safe:
            raise ValueError(f"Unsafe command: {reason}")
        
        # Get safe working directory
        work_dir = get_safe_working_directory(working_directory)
        
        # Prepare environment
        env = os.environ.copy()
        if env_vars:
            env.update(env_vars)
        
        logger.info(f"[terminal_exec] Executing: {command} in {work_dir}")
        
        # Execute command
        start_time = time.time()
        
        if capture_output:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=work_dir,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=shell
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout
                )
                
                stdout_text = stdout.decode('utf-8', errors='ignore') if stdout else ""
                stderr_text = stderr.decode('utf-8', errors='ignore') if stderr else ""
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise ValueError(f"Command timed out after {timeout} seconds")
        else:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=work_dir,
                env=env,
                shell=shell
            )
            
            try:
                await asyncio.wait_for(process.wait(), timeout=timeout)
                stdout_text = ""
                stderr_text = ""
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise ValueError(f"Command timed out after {timeout} seconds")
        
        execution_time = time.time() - start_time
        
        result = {
            "command": command,
            "working_directory": work_dir,
            "return_code": process.returncode,
            "execution_time": execution_time,
            "success": process.returncode == 0,
            "stdout": stdout_text,
            "stderr": stderr_text,
            "timeout": timeout,
            "captured_output": capture_output
        }
        
        logger.info(f"[terminal_exec] Command completed with return code: {process.returncode}")
        success = "error"
        if result["success"]:
            success = "success"
        
        final_response = {"status": success, "content": result}
        return final_response, search_references
        
    except Exception as e:
        logger.error(f"[terminal_exec] Error: {str(e)}")
        result = {"status": "error", "content": f"Terminal execution failed: {str(e)}"}
        return result, search_references


async def process_process_monitor(
    action: str,
    process_name: Optional[str] = None,
    command: Optional[str] = None,
    working_directory: Optional[str] = None,
    process_id: Optional[int] = None,
    signal_name: str = "SIGTERM",
    tool_id: str = "",
    search_references: Any = None
) -> tuple[Dict[str, Any], Any]:
    """
    Start, stop, and monitor running processes.
    
    Args:
        action: Action to perform ('start', 'stop', 'status', 'list', 'kill')
        process_name: Name or identifier of the process
        command: Command to start the process
        working_directory: Working directory for process
        process_id: Process ID for operations
        signal_name: Signal to send when stopping
        tool_id: Tool execution ID
        search_references: Search references object
    
    Returns:
        Tuple of (result dict, search_references)
    """
    try:
        logger.info(f"[process_monitor] Action: {action}")
        
        if action == "start":
            if not command:
                raise ValueError("Command required for start action")
            
            # Validate command
            is_safe, reason = validate_command(command)
            if not is_safe:
                raise ValueError(f"Unsafe command: {reason}")
            
            work_dir = get_safe_working_directory(working_directory)
            
            # Start process
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=work_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Register process
            proc_name = process_name or f"proc_{process.pid}"
            PROCESS_REGISTRY[proc_name] = {
                "pid": process.pid,
                "command": command,
                "working_directory": work_dir,
                "started_at": time.time(),
                "process": process
            }
            
            result = {
                "action": "start",
                "process_name": proc_name,
                "pid": process.pid,
                "command": command,
                "working_directory": work_dir,
                "success": True
            }
            
        elif action == "stop":
            if process_name and process_name in PROCESS_REGISTRY:
                proc_info = PROCESS_REGISTRY[process_name]
                process = proc_info["process"]
                
                # Send signal to stop process
                if signal_name == "SIGKILL":
                    process.kill()
                else:
                    process.terminate()
                
                await process.wait()
                del PROCESS_REGISTRY[process_name]
                
                result = {
                    "action": "stop",
                    "process_name": process_name,
                    "signal": signal_name,
                    "success": True
                }
            elif process_id:
                try:
                    proc = psutil.Process(process_id)
                    if signal_name == "SIGKILL":
                        proc.kill()
                    else:
                        proc.terminate()
                    
                    result = {
                        "action": "stop",
                        "process_id": process_id,
                        "signal": signal_name,
                        "success": True
                    }
                except psutil.NoSuchProcess:
                    raise ValueError(f"Process not found: {process_id}")
            else:
                raise ValueError("Process name or ID required for stop action")
                
        elif action == "status":
            if process_name and process_name in PROCESS_REGISTRY:
                proc_info = PROCESS_REGISTRY[process_name]
                process = proc_info["process"]
                
                is_running = process.returncode is None
                
                result = {
                    "action": "status",
                    "process_name": process_name,
                    "pid": proc_info["pid"],
                    "running": is_running,
                    "return_code": process.returncode,
                    "started_at": proc_info["started_at"],
                    "uptime": time.time() - proc_info["started_at"] if is_running else None
                }
            elif process_id:
                try:
                    proc = psutil.Process(process_id)
                    result = {
                        "action": "status",
                        "process_id": process_id,
                        "running": proc.is_running(),
                        "name": proc.name(),
                        "cpu_percent": proc.cpu_percent(),
                        "memory_percent": proc.memory_percent(),
                        "create_time": proc.create_time()
                    }
                except psutil.NoSuchProcess:
                    result = {
                        "action": "status",
                        "process_id": process_id,
                        "running": False,
                        "error": "Process not found"
                    }
            else:
                raise ValueError("Process name or ID required for status action")
                
        elif action == "list":
            # List registered processes
            registered_processes = []
            for name, info in PROCESS_REGISTRY.items():
                process = info["process"]
                is_running = process.returncode is None
                
                registered_processes.append({
                    "name": name,
                    "pid": info["pid"],
                    "command": info["command"],
                    "running": is_running,
                    "started_at": info["started_at"],
                    "uptime": time.time() - info["started_at"] if is_running else None
                })
            
            # List system processes (limited to current user)
            system_processes = []
            try:
                current_user = psutil.Process().username()
                for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent']):
                    try:
                        if proc.info['username'] == current_user:
                            system_processes.append({
                                "pid": proc.info['pid'],
                                "name": proc.info['name'],
                                "cpu_percent": proc.info['cpu_percent']
                            })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            except Exception as e:
                logger.warning(f"Could not list system processes: {str(e)}")
            
            result = {
                "action": "list",
                "registered_processes": registered_processes,
                "system_processes": system_processes[:20],  # Limit to 20 processes
                "total_registered": len(registered_processes),
                "total_system": len(system_processes)
            }
            
        elif action == "kill":
            # Force kill process
            if process_name and process_name in PROCESS_REGISTRY:
                proc_info = PROCESS_REGISTRY[process_name]
                process = proc_info["process"]
                process.kill()
                await process.wait()
                del PROCESS_REGISTRY[process_name]
                
                result = {
                    "action": "kill",
                    "process_name": process_name,
                    "success": True
                }
            elif process_id:
                try:
                    proc = psutil.Process(process_id)
                    proc.kill()
                    result = {
                        "action": "kill",
                        "process_id": process_id,
                        "success": True
                    }
                except psutil.NoSuchProcess:
                    raise ValueError(f"Process not found: {process_id}")
            else:
                raise ValueError("Process name or ID required for kill action")
        else:
            raise ValueError(f"Unsupported action: {action}")
        
        logger.info(f"[process_monitor] {action} completed successfully")
        success = "error"
        if result:
            success = "success"
        
        final_response = {"status": success, "content": result}
        return final_response, search_references
        
    except Exception as e:
        logger.error(f"[process_monitor] Error: {str(e)}")
        result = {"status": "error", "content": f"Process monitoring failed: {str(e)}"}

        return result, search_references
