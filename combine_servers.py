import asyncio
import uvicorn
import multiprocessing
import signal
import sys
from http_server import app as http_app
from websocket_server import app as websocket_app


def run_http_server():
    """Run the HTTP server on port 45213 with 4 workers"""
    print("Starting HTTP server on port 45213 with 4 workers...")
    uvicorn.run("http_server:app", host="127.0.0.1", port=45213, workers=4, log_level="info")


def run_websocket_server():
    """Run the WebSocket server on port 45214 with single worker"""
    print("Starting WebSocket server on port 45214 with single worker...")
    uvicorn.run("websocket_server:app", host="127.0.0.1", port=45214, workers=1, log_level="info")


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print("\nShutting down servers...")
    sys.exit(0)


def main():
    """Main function to start both servers simultaneously"""
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("CodeMate Server Suite")
    print("====================")
    print("Starting HTTP and WebSocket servers...")
    
    # Create processes for both servers
    http_process = multiprocessing.Process(target=run_http_server, name="HTTP-Server")
    websocket_process = multiprocessing.Process(target=run_websocket_server, name="WebSocket-Server")
    
    try:
        # Start both servers
        http_process.start()
        websocket_process.start()
        
        print("\n✅ Both servers are running:")
        print("   📡 HTTP API: http://127.0.0.1:45213")
        print("   🔌 WebSocket: ws://127.0.0.1:45214/ws")
        print("\nPress Ctrl+C to stop both servers")
        
        # Wait for both processes
        http_process.join()
        websocket_process.join()
        
    except KeyboardInterrupt:
        print("\nReceived shutdown signal...")
    except Exception as e:
        print(f"Error running servers: {e}")
    finally:
        # Ensure both processes are terminated
        if http_process.is_alive():
            print("Terminating HTTP server...")
            http_process.terminate()
            http_process.join(timeout=5)
            if http_process.is_alive():
                http_process.kill()
        
        if websocket_process.is_alive():
            print("Terminating WebSocket server...")
            websocket_process.terminate()
            websocket_process.join(timeout=5)
            if websocket_process.is_alive():
                websocket_process.kill()
        
        print("All servers stopped.")


if __name__ == "__main__":
    main()
