# Apple Developer Configuration (for macOS builds)
APPLE_TEAM_ID=XXXXXX
APPLE_DEVELOPER_NAME="Your Name"
APPLE_ID="<EMAIL>"
APPLE_APP_PASSWORD="your-app-specific-password"

# =============================================================================
# LiteLLM API Keys Configuration
# =============================================================================
# Configure API keys for different LLM providers
# Only add the providers you want to use - unused keys can be left empty

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Anthropic Configuration
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# Google AI Configuration
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your-google-ai-api-key-here

# Azure OpenAI Configuration
# Get these from your Azure OpenAI resource in the Azure portal
AZURE_OPENAI_API_KEY=your-azure-openai-api-key-here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name



MY_MODEL_API_ENDPOINT=https://your_developement-endpoint.com

# =============================================================================
# Security Notes:
# =============================================================================
# 1. NEVER commit the actual .env file to version control
# 2. Keep your API keys secure and rotate them regularly
# 3. Use different API keys for development and production
# 4. Monitor your API usage and set up billing alerts
# 5. Consider using Azure Key Vault or similar for production deployments