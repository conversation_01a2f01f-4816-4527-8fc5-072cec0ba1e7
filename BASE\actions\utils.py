import copy

class SearchReferences:
    def __init__(self, request_id: str):
        self.search_results = {"request_id": request_id, "results": []}

    def add_search_result(self, path: str, type: str, name: str, content: str):
        self.search_results["results"].append(
            {"path": path, "type": type, "name": name, "content": content}
        )

    def get_search_result(self):
        return copy.deepcopy(self.search_results)