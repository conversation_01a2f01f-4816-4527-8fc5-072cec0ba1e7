import asyncio
import httpx
from typing import Union
import constants

async def generate_cloud_embeddings(batch: bool, texts: Union[str, list[str]]) -> Union[list[float], list[list[float]]]:
    """Generate embeddings using cloud service."""
    if not batch:
        if isinstance(texts, str):
            texts = [texts]
        elif isinstance(texts, list) and len(texts) > 0:
            texts = [texts[0]]
        else:
            raise ValueError("Provide at least one text string.")
    elif isinstance(texts, str):
        texts = [texts]

    base_url = constants.embeddings
    error = None
    retry_count = 10

    while retry_count > 0:
        try:
            # print(f"[Cloud Embeddings] Attempting to generate embeddings for {len(texts)} texts")
            # print(f"[Cloud Embeddings] Using URL: {base_url}/generate")
            
            async with httpx.AsyncClient(verify=constants.SSL_CONTEXT) as client:
                response = await client.post(
                    f"{base_url}/generate",
                    json={"texts": texts},
                    timeout=30.0,
                )
                
                # print(f"[Cloud Embeddings] Response status: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"[Cloud Embeddings] Error response: {response.text}")
                    
            response.raise_for_status()
            body = response.json()
            # print(f"[Cloud Embeddings] Response body structure: {list(body[0].keys()) if body else 'Empty response'}")
            
            embeddings = [item["embedding"] for item in body]
            # print(f"[Cloud Embeddings] Successfully generated {len(embeddings)} embeddings")

            return embeddings if batch else embeddings[0]

        except httpx.HTTPError as e:
            retry_count -= 1
            await asyncio.sleep(1)
            error = e
            # print(f"[Cloud Embeddings] HTTP Error: {str(e)}")
            # print(f"[Cloud Embeddings] Retries left: {retry_count}")
            
        except Exception as e:
            retry_count -= 1
            await asyncio.sleep(1)
            error = e
            # print(f"[Cloud Embeddings] Unexpected error: {str(e)}")
            # print(f"[Cloud Embeddings] Error type: {type(e).__name__}")
            # print(f"[Cloud Embeddings] Retries left: {retry_count}")

    # print("[Cloud Embeddings] All retries exhausted")
    raise error
