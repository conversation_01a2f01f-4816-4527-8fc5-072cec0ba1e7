"""
Configuration management for GitControl
"""

import os
import json
from pathlib import Path
from typing import Dict, Optional, Any


class GitControlConfig:
    """Handles configuration for GitControl"""
    
    def __init__(self, config_file: str = ".gitcontrol-config"):
        self.config_file = Path.home() / config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    self.config_data = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config file: {e}")
                self.config_data = {}
        else:
            self.config_data = {}
    
    def save_config(self) -> None:
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config_data, f, indent=2)
        except IOError as e:
            print(f"Error: Could not save config file: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        keys = key.split('.')
        data = self.config_data
        
        for k in keys[:-1]:
            if k not in data:
                data[k] = {}
            data = data[k]
        
        data[keys[-1]] = value
        self.save_config()
    
    def get_token(self, provider: str) -> Optional[str]:
        """Get authentication token for a provider (github, gitlab, etc.)"""
        return self.get(f"tokens.{provider}")
    
    def set_token(self, provider: str, token: str) -> None:
        """Set authentication token for a provider"""
        self.set(f"tokens.{provider}", token)
    
    def get_default_branch(self) -> str:
        """Get default branch name"""
        return self.get("default_branch", "main")
    
    def set_default_branch(self, branch: str) -> None:
        """Set default branch name"""
        self.set("default_branch", branch)
    
    def get_git_directories(self) -> list:
        """Get list of directories to search for git repositories"""
        return self.get("git_directories", [str(Path.home())])
    
    def add_git_directory(self, directory: str) -> None:
        """Add directory to search for git repositories"""
        dirs = self.get_git_directories()
        if directory not in dirs:
            dirs.append(directory)
            self.set("git_directories", dirs)
    
    def get_user_info(self, provider: str) -> Dict[str, str]:
        """Get user information for a provider"""
        return self.get(f"users.{provider}", {})
    
    def set_user_info(self, provider: str, username: str, email: str = None) -> None:
        """Set user information for a provider"""
        user_info = {"username": username}
        if email:
            user_info["email"] = email
        self.set(f"users.{provider}", user_info)
